@tailwind base;
@tailwind components;
@tailwind utilities;


@import url('https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap');
@import "slick-carousel/slick/slick.css";
@import "slick-carousel/slick/slick-theme.css";

@layer base {
  * {
    font-family: 'Figtree', ui-sans-serif, system-ui, sans-serif !important;
  }
}



body {
  width: 100vw; /* Set the body width to full viewport width */
  margin: 0; /* Remove default margin */
  padding: 0; /* Remove default padding */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
}


html,
body,
#root,
.app {
  height: 100%;
  width: 100%;
  font-family: 'Figtree', ui-sans-serif, system-ui, sans-serif !important;
}

/* Persistent Suspense Component Styles */
.persistent-suspense-container {
  position: relative;
  width: 100%;
  transition: opacity 0.3s ease-in-out;
}

.persistent-component {
  width: 100%;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease-in-out;
}

.persistent-suspense-error {
  width: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Prevent component unmounting on mobile scroll */
@media (max-width: 768px) {
  .persistent-suspense-container {
    /* Force hardware acceleration for smoother scrolling */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .persistent-component {
    /* Ensure components stay in memory */
    contain: layout style paint;
    will-change: auto;
  }
}

.input-box-shadow {
  filter: drop-shadow(-4px -4px 44px rgba(0, 0, 0, 0.08));
}

.slick-prev:before,
.slick-next:before {
  color: black;
  font-size: 40px;
}

.slick-next:before {
  margin-left: -25px;
}

.slick-prev {
  z-index: 9999;
}

.category:hover,
.icon {
  fill: #015681;
}


/* Existing Button Styles */
.buttons {
  font-weight: 900;
  color: white;
  /* Text color */
  border-radius: 30px;
  z-index: 1;
  border: 1px transparent solid;
  background: #674941;
  /* Original background color */
  position: relative;
  transition: all 250ms;
  overflow: hidden;
  /* Ensure the pseudo-elements don't overflow */
  text-transform: uppercase;
  /* From SCSS */
  font-size: 1rem;
  /* From SCSS */
  letter-spacing: .15rem;
  /* From SCSS */
}

/* Pseudo-element before for hover effect */
.buttons::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  border-radius: 30px;
  background-color: #95725D;
  /* Sky blue background on hover */
  z-index: -1;
  transition: all 500ms;
}



/* Hover effect */
.buttons:hover {

  /* Blue border on hover */
  color: white;
  /* Text color on hover */
}

.buttons:hover::before {
  width: 100%;
}

.buttons:hover::after {
  width: 0;
  /* Optional: hide or change after hover */
}


/* Custom CSS for pseudo-element */
.button {
  font-weight: 900;
  color: black;
  /* Text color */
  border-radius: 30px;
  z-index: 1;
  border: 1px transparent solid;
  background: white;
  /* Original background color */
  position: relative;
  transition: all 250ms;
  overflow: hidden;
  /* Ensure the pseudo-elements don't overflow */
  text-transform: uppercase;
  /* From SCSS */
  font-size: 1rem;
  /* From SCSS */
  letter-spacing: .15rem;
  /* From SCSS */
}

/* Pseudo-element before for hover effect */
.button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  border-radius: 30px;
  background-color: #025add;
  /* Sky blue background on hover */
  z-index: -1;
  transition: all 500ms;
}



/* Hover effect */
.button:hover {
  border: 1px #0088cc solid;
  /* Blue border on hover */
  color: white;
  /* Text color on hover */
}

.button:hover::before {
  width: 100%;
}

.button:hover::after {
  width: 0;
  /* Optional: hide or change after hover */
}



.card:hover {
  color: white;
}

.card:hover p {
  color: rgba(255, 255, 255, 0.718);
}

.card:hover h3 {
  color: rgba(255, 255, 255, 0.718);
}

.card:hover .aboutIcon {
  color: white;
}

.card:hover button {
  color: white;
}

.card:hover::before {
  width: 100%;
}


.gradient-text-button {
  background: linear-gradient(115deg, #f9ce34, #ee2a7b, #6228d7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

}


/* @keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 20s linear infinite;
  
  
  
} */

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Default animation for larger screens */
.animate-scroll {
  animation: scroll 20s linear infinite;
}

/* Increase animation speed for mobile screens (≤ 640px) */
@media (max-width: 640px) {
  .animate-scroll {
    animation: scroll 10s linear infinite; /* Increased speed on mobile */
  }
}

/* Optionally, for other breakpoints, you can set different speeds */
@media (max-width: 768px) {
  .animate-scroll {
    animation: scroll 10s linear infinite; /* Slightly faster for tablets */
  }


}


@media (max-width: 320px) {
  .animate-scroll {
    animation: scroll 5s linear infinite; /* Slightly faster for tablets */
  }


}

/* Infinite vertical scrolling animations for hero section columns */
@keyframes scrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

@keyframes scrollDown {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-scroll-up {
  animation: scrollUp 20s linear infinite;
}

.animate-scroll-down {
  animation: scrollDown 20s linear infinite;
}

/* Responsive animation speeds for vertical scrolling */
@media (max-width: 768px) {
  .animate-scroll-up {
    animation: scrollUp 15s linear infinite;
  }

  .animate-scroll-down {
    animation: scrollDown 15s linear infinite;
  }
}

@media (max-width: 640px) {
  .animate-scroll-up {
    animation: scrollUp 12s linear infinite;
  }

  .animate-scroll-down {
    animation: scrollDown 12s linear infinite;
  }
}

/* Horizontal Left Scroll - Updated for triple images */
@keyframes scrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.333%);
  }
}

/* Horizontal Right Scroll - Updated for triple images */
@keyframes scrollRight {
  0% {
    transform: translateX(-33.333%);
  }
  100% {
    transform: translateX(0);
  }
}

/* Default animation speeds - Optimized for infinite scroll */
.animate-scroll-left {
  animation: scrollLeft 25s linear infinite;
}

.animate-scroll-right {
  animation: scrollRight 25s linear infinite;
}

/* Responsive Speeds - Consistent infinite scrolling */
@media (max-width: 768px) {
  .animate-scroll-left {
    animation: scrollLeft 20s linear infinite;
  }
  .animate-scroll-right {
    animation: scrollRight 20s linear infinite;
  }
}

@media (max-width: 640px) {
  .animate-scroll-left {
    animation: scrollLeft 15s linear infinite;
  }
  .animate-scroll-right {
    animation: scrollRight 15s linear infinite;
  }
}

@media (max-width: 320px) {
  .animate-scroll-left {
    animation: scrollLeft 12s linear infinite;
  }
  .animate-scroll-right {
    animation: scrollRight 12s linear infinite;
  }
}

.custom-swiper .swiper-button-next,
.custom-swiper .swiper-button-prev {
  color: #563D39; 
}

/* Blog Skeleton and Blog Content Custom Classes */
.content-container {
  color: #3d2a26;
  max-width: 1100px;
  margin: 0 auto;
  line-height: 1.8;
  padding: 0 2em;
}
.content-container p {
  font-size: 1.15rem;
  margin-bottom: 1.5em;
}
.content-container ul,
.content-container ol {
  font-size: 1.1rem;
  margin: 1.5em 0;
  padding-left: 1.5em;
}
.content-container li {
  margin-bottom: 0.75em;
}
.content-container h1 {
  font-size: 2.5em;
  margin: 1.5em 0 1em;
  color: #563D39;
  font-weight: bold;
}
.content-container h2 {
  font-size: 2em;
  margin: 1.5em 0 1em;
  color: #563D39;
  font-weight: bold;
}
.content-container h3 {
  font-size: 1.5em;
  margin: 1.5em 0 1em;
  color: #563D39;
  font-weight: bold;
}
.content-container strong,
.content-container b {
  color: #563D39;
}
.content-container blockquote {
  font-size: 1.25rem;
  line-height: 1.6;
  border-left: 4px solid #563D39;
  background: #f8f6f5;
  padding: 1.25em 1.5em;
  margin: 2em 0;
  border-radius: 0.5em;
  color: #563D39;
}
.content-container pre {
  font-size: 1rem;
  line-height: 1.6;
  background: #f3f4f6;
  padding: 1em;
  border-radius: 0.5em;
  margin: 2em 0;
  overflow-x: auto;
}
.content-container img {
  margin: 2em auto;
  border-radius: 0.75em;
  box-shadow: 0 4px 24px 0 rgba(86,61,57,0.08);
  max-width: 100%;
  height: auto;
  display: block;
}
.hero-image {
  width: 100%;
  max-width: 100%;
  aspect-ratio: 16 / 4.5;
  object-fit: cover;
  border-radius: 2em;
  box-shadow: 0 4px 24px 0 rgba(86, 61, 57, 0.08);
  display: block;
  margin: 0 auto;
}
.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75em;
  justify-content: center;
  margin: 1.5em 0 2em 0;
}
.meta-badge {
  background: #563D39;
  color: #fff;
  padding: 0.4em 1.2em;
  border-radius: 999px;
  font-size: 0.95em;
  font-weight: 500;
  opacity: 0.92;
}
@media (max-width: 1024px) {
  .content-container {
    padding: 0 1em;
  }
  .hero-image {
    max-height: 350px;
  }
}
@media (max-width: 768px) {
  .content-container {
    padding: 0 0.5em;
  }
  .content-container p,
  .content-container ul,
  .content-container ol {
    font-size: 1rem;
  }
  .content-container h1 {
    font-size: 2em;
  }
  .content-container h2 {
    font-size: 1.5em;
  }
  .content-container h3 {
    font-size: 1.2em;
  }
  .hero-image {
    max-height: 220px;
    border-radius: 1em !important;
  }
  .meta-info {
    flex-direction: column;
    align-items: center;
    gap: 0.5em;
  }
}
@media (max-width: 480px) {
  .content-container {
    padding: 0 0.25em;
  }
  .content-container p,
  .content-container ul,
  .content-container ol {
    font-size: 0.95rem;
  }
  .content-container h1 {
    font-size: 1.3em;
  }
  .content-container h2 {
    font-size: 1.1em;
  }
  .content-container h3 {
    font-size: 1em;
  }

  .meta-info {
    flex-direction: column;
    align-items: stretch;
    gap: 0.4em;
  }
  .meta-badge {
    font-size: 0.85em;
    padding: 0.3em 0.8em;
  }
}

