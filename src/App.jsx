import "./App.css";
import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./i18n";
import Layout from "./Layout";

// PersistentSuspense not needed since we're importing components directly

// Immediate load components (critical for first paint)
import Main_section from "./components/main_herosection/Main_section";
import ManagementFeatures from "./components/ManagementFeatures/ManagementFeatures";

// Import About_Us_Hero immediately to prevent unmounting
import About_Us_Hero from "./components/AboutUs/AboutUsHero";

// Import home page components directly to prevent unmounting issues
import SocialMediaIcons from "./components/social_media/SocialMediaIcons";
import InfoCard from "./components/InfoCard/InfoCard";
import Thoughts from "./components/Thoughts/Thoughts";
import Testimonials from "./components/Testimonials/Testimonials";
import TextReveal from "./components/TextReveal/TextReveal";
import FAQ from "./components/FAQ/FAQ";

// Keep lazy loading for other route components only
const About_Us = React.lazy(() => import("./components/AboutUs/index"));

// Lazy load other route components
const Delete = React.lazy(() => import("./components/Delete"));
const Terms = React.lazy(() => import("./components/Terms/Terms"));
const ContactUs = React.lazy(() => import("./components/Contactus/ContactUs"));
const Policy = React.lazy(() => import("./components/Privacy_Policy/Policy"));
const RedirectingLinks = React.lazy(() =>
  import("./components/RedirectingLinks/RedirectingLinks")
);
const ShareProfile = React.lazy(() =>
  import("./components/ShareProfile/ShareProfile")
);
const PDFViewer = React.lazy(() => import("./components/vishal/vishal"));
const MilanCard = React.lazy(() => import("./components/milan/milan"));
const SharePost = React.lazy(() =>
  import("./components/ShareProfile/SharePost")
);
const Features = React.lazy(() => import("./components/Features/Features"));
const Blogs = React.lazy(() => import("./components/Blogs/index"));
const SingleBlogView = React.lazy(() =>
  import("./components/Blogs/SingleBlogView")
);

// Import loading components (only needed for other routes)
import {
  LoadingSpinner,
  AboutUsSkeleton,
  BlogsSkeleton,
} from "./components/LoadingComponents";

// Import performance monitor for development

function App() {
  return (
    <Router>
      <Routes>
        {/* Routes inside Layout (Navbar and Footer included) */}
        <Route element={<Layout />}>
          <Route
            path="/"
            element={
              <>
                {/* Load hero section immediately for fast first paint */}
                <Main_section />

                {/* Load management features immediately - no lazy loading for critical sections */}
                <ManagementFeatures />

                {/* All components imported directly - no lazy loading to prevent unmounting */}
                <SocialMediaIcons />
                <InfoCard />
                <About_Us_Hero />
                <Thoughts />
                <Testimonials />
                <TextReveal />
                <FAQ />
              </>
            }
          />

          <Route
            path="/aboutus"
            element={
              <Suspense fallback={<AboutUsSkeleton />}>
                <About_Us />
              </Suspense>
            }
          />
          <Route
            path="/solutions"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <Features />
              </Suspense>
            }
          />
          <Route
            path="/blogs"
            element={
              <Suspense fallback={<BlogsSkeleton />}>
                <Blogs />
              </Suspense>
            }
          />
          <Route
            path="/blog/:slug"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <SingleBlogView />
              </Suspense>
            }
          />
          <Route
            path="/testimonials"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <Testimonials />
              </Suspense>
            }
          />
          <Route
            path="/delete"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <Delete />
              </Suspense>
            }
          />
          <Route
            path="/terms"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <Terms />
              </Suspense>
            }
          />
          <Route
            path="/privacy-policy"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <Policy />
              </Suspense>
            }
          />
          <Route
            path="/contact-us"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <ContactUs />
              </Suspense>
            }
          />
        </Route>

        <Route
          path="/app"
          element={
            <Suspense fallback={<LoadingSpinner />}>
              <RedirectingLinks />
            </Suspense>
          }
        />
        <Route
          path="/share-profile"
          element={
            <Suspense fallback={<LoadingSpinner />}>
              <ShareProfile />
            </Suspense>
          }
        />
        <Route
          path="/employees/FR-00103_vishal-kathiriya"
          element={
            <Suspense fallback={<LoadingSpinner />}>
              <PDFViewer />
            </Suspense>
          }
        />
        <Route
          path="/employees/FR-3489720_milan-katrodiya"
          element={
            <Suspense fallback={<LoadingSpinner />}>
              <MilanCard />
            </Suspense>
          }
        />
        <Route
          path="/get-post"
          element={
            <Suspense fallback={<LoadingSpinner />}>
              <SharePost />
            </Suspense>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;
