import { useEffect, useRef } from 'react';

/**
 * Hook to monitor component lifecycle for debugging
 * Helps identify when components are mounting/unmounting unexpectedly
 */
export const useComponentLifecycle = (componentName, debug = false) => {
  const mountTimeRef = useRef(null);
  const renderCountRef = useRef(0);

  useEffect(() => {
    mountTimeRef.current = Date.now();
    renderCountRef.current += 1;

    if (debug) {
      console.log(`🟢 ${componentName} mounted at ${new Date().toLocaleTimeString()}`);
      console.log(`📊 ${componentName} render count: ${renderCountRef.current}`);
    }

    // Cleanup function runs when component unmounts
    return () => {
      const mountDuration = mountTimeRef.current 
        ? Date.now() - mountTimeRef.current 
        : 0;

      if (debug) {
        console.log(`🔴 ${componentName} unmounted after ${mountDuration}ms`);
        console.log(`📊 ${componentName} final render count: ${renderCountRef.current}`);
      }
    };
  }, [componentName, debug]);

  // Track re-renders
  useEffect(() => {
    if (debug && renderCountRef.current > 1) {
      console.log(`🔄 ${componentName} re-rendered (count: ${renderCountRef.current})`);
    }
  });

  return {
    renderCount: renderCountRef.current,
    mountTime: mountTimeRef.current
  };
};

/**
 * Hook to detect if component is being unmounted due to scrolling
 */
export const useScrollUnmountDetection = (componentName, debug = false) => {
  const elementRef = useRef(null);
  const isVisibleRef = useRef(true);

  useEffect(() => {
    if (!elementRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const wasVisible = isVisibleRef.current;
          const isVisible = entry.isIntersecting;
          isVisibleRef.current = isVisible;

          if (debug) {
            if (wasVisible && !isVisible) {
              console.log(`👁️ ${componentName} scrolled out of view`);
            } else if (!wasVisible && isVisible) {
              console.log(`👁️ ${componentName} scrolled into view`);
            }
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observer.observe(elementRef.current);

    return () => {
      observer.disconnect();
    };
  }, [componentName, debug]);

  return {
    elementRef,
    isVisible: isVisibleRef.current
  };
};

/**
 * Hook to prevent component unmounting on mobile
 */
export const usePreventMobileUnmount = (componentName) => {
  const elementRef = useRef(null);
  const isMobileRef = useRef(false);

  useEffect(() => {
    const checkMobile = () => {
      isMobileRef.current = window.innerWidth <= 768;
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  useEffect(() => {
    if (!elementRef.current || !isMobileRef.current) return;

    // Force the element to stay in the DOM on mobile
    const element = elementRef.current;
    
    // Add CSS properties to prevent unmounting
    element.style.contain = 'layout style paint';
    element.style.willChange = 'auto';
    element.style.transform = 'translateZ(0)';

    return () => {
      // Cleanup styles if component unmounts
      if (element) {
        element.style.contain = '';
        element.style.willChange = '';
        element.style.transform = '';
      }
    };
  }, []);

  return {
    elementRef,
    isMobile: isMobileRef.current
  };
};
