import React, { useState, useRef, useEffect, createContext, useContext } from "react";

/**
 * Component Cache Context
 * Manages a global cache of mounted components to prevent unmounting
 */
const ComponentCacheContext = createContext({
  cache: new Map(),
  addToCache: () => {},
  removeFromCache: () => {},
  isInCache: () => false,
  getCachedComponent: () => null
});

/**
 * Component Cache Provider
 * Provides component caching functionality throughout the app
 */
export const ComponentCacheProvider = ({ children }) => {
  const cacheRef = useRef(new Map());
  const [, forceUpdate] = useState({});

  const addToCache = (key, component) => {
    cacheRef.current.set(key, component);
    forceUpdate({});
  };

  const removeFromCache = (key) => {
    cacheRef.current.delete(key);
    forceUpdate({});
  };

  const isInCache = (key) => {
    return cacheRef.current.has(key);
  };

  const getCachedComponent = (key) => {
    return cacheRef.current.get(key);
  };

  const clearCache = () => {
    cacheRef.current.clear();
    forceUpdate({});
  };

  const value = {
    cache: cacheRef.current,
    addToCache,
    removeFromCache,
    isInCache,
    getCachedComponent,
    clearCache
  };

  return (
    <ComponentCacheContext.Provider value={value}>
      {children}
    </ComponentCacheContext.Provider>
  );
};

/**
 * Hook to use component cache
 */
export const useComponentCache = () => {
  const context = useContext(ComponentCacheContext);
  if (!context) {
    throw new Error('useComponentCache must be used within a ComponentCacheProvider');
  }
  return context;
};

/**
 * Cached Component Wrapper
 * Ensures components stay mounted once loaded
 */
export const CachedComponent = ({ 
  cacheKey, 
  children, 
  fallback = <div>Loading...</div>,
  keepMounted = true,
  className = ""
}) => {
  const { addToCache, isInCache, getCachedComponent } = useComponentCache();
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const componentRef = useRef(null);

  // Check if component is already cached
  const isCached = isInCache(cacheKey);
  const cachedComponent = getCachedComponent(cacheKey);

  useEffect(() => {
    if (isCached && cachedComponent) {
      setIsLoaded(true);
    }
  }, [isCached, cachedComponent]);

  const handleComponentLoad = (loadedComponent) => {
    if (keepMounted && !isCached) {
      addToCache(cacheKey, loadedComponent);
    }
    setIsLoaded(true);
  };

  const handleError = (error) => {
    console.error(`CachedComponent (${cacheKey}) error:`, error);
    setHasError(true);
  };

  // If there's an error, show fallback
  if (hasError) {
    return (
      <div className={`cached-component-error ${className}`}>
        {fallback}
      </div>
    );
  }

  // If component is cached and loaded, show cached version
  if (isCached && isLoaded && keepMounted) {
    return (
      <div 
        ref={componentRef}
        className={`cached-component-container ${className}`}
        data-cache-key={cacheKey}
      >
        {cachedComponent || children}
      </div>
    );
  }

  // Show loading state or render component for first time
  return (
    <div 
      ref={componentRef}
      className={`cached-component-container loading ${className}`}
      data-cache-key={cacheKey}
    >
      {isLoaded ? (
        <ComponentWrapper 
          onLoad={handleComponentLoad} 
          onError={handleError}
          cacheKey={cacheKey}
        >
          {children}
        </ComponentWrapper>
      ) : (
        fallback
      )}
    </div>
  );
};

/**
 * Component wrapper to handle loading and caching
 */
const ComponentWrapper = ({ children, onLoad, onError, cacheKey }) => {
  const wrapperRef = useRef(null);

  useEffect(() => {
    // Component has mounted successfully
    if (wrapperRef.current) {
      onLoad(wrapperRef.current);
    }
  }, [onLoad]);

  useEffect(() => {
    const handleUnhandledRejection = (event) => {
      onError(event.reason);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  try {
    return (
      <div ref={wrapperRef} data-component-wrapper={cacheKey}>
        {children}
      </div>
    );
  } catch (error) {
    onError(error);
    return null;
  }
};

/**
 * Higher-order component for automatic caching
 */
export const withComponentCache = (WrappedComponent, options = {}) => {
  const { 
    cacheKey: defaultCacheKey,
    keepMounted = true, 
    fallback = <div>Loading...</div>,
    displayName = 'CachedComponent'
  } = options;

  const CachedWrappedComponent = (props) => {
    const cacheKey = props.cacheKey || defaultCacheKey || WrappedComponent.name || 'unknown';
    
    return (
      <CachedComponent 
        cacheKey={cacheKey}
        fallback={fallback} 
        keepMounted={keepMounted}
      >
        <WrappedComponent {...props} />
      </CachedComponent>
    );
  };

  CachedWrappedComponent.displayName = displayName;
  return CachedWrappedComponent;
};

/**
 * Hook for manual component caching
 */
export const useManualCache = (cacheKey) => {
  const { addToCache, removeFromCache, isInCache, getCachedComponent } = useComponentCache();
  
  const cacheComponent = (component) => {
    addToCache(cacheKey, component);
  };

  const uncacheComponent = () => {
    removeFromCache(cacheKey);
  };

  const isCached = isInCache(cacheKey);
  const cachedComponent = getCachedComponent(cacheKey);

  return {
    cacheComponent,
    uncacheComponent,
    isCached,
    cachedComponent
  };
};
