import React, { Suspense, useState, useRef, useEffect } from "react";

/**
 * PersistentSuspense Component
 * 
 * This component ensures that once a lazy-loaded component is mounted,
 * it stays mounted even when scrolling away from it on mobile devices.
 * This prevents the unmounting/remounting issue that causes components
 * to reload when scrolling back to them.
 */
const PersistentSuspense = ({ 
  children, 
  fallback, 
  keepMounted = true,
  className = ""
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const componentRef = useRef(null);
  const mountedComponentRef = useRef(null);

  // Error boundary functionality
  const handleError = (error) => {
    console.error("PersistentSuspense caught an error:", error);
    setHasError(true);
  };

  // Reset error state when children change
  useEffect(() => {
    if (hasError) {
      setHasError(false);
    }
  }, [children, hasError]);

  // Handle component loading
  const handleComponentLoad = () => {
    setIsLoaded(true);
  };

  // If there's an error, show fallback
  if (hasError) {
    return (
      <div className={`persistent-suspense-error ${className}`}>
        {fallback}
      </div>
    );
  }

  return (
    <div 
      ref={componentRef}
      className={`persistent-suspense-container ${className}`}
      style={{
        // Ensure the container maintains its space
        minHeight: isLoaded ? 'auto' : '200px',
        position: 'relative'
      }}
    >
      {/* Always render the component once loaded to keep it mounted */}
      {isLoaded && keepMounted && (
        <div 
          ref={mountedComponentRef}
          className="persistent-component"
          style={{
            // Keep component in DOM but manage visibility if needed
            opacity: 1,
            visibility: 'visible'
          }}
        >
          {children}
        </div>
      )}
      
      {/* Show suspense boundary only when not loaded */}
      {!isLoaded && (
        <Suspense fallback={fallback}>
          <ComponentWrapper onLoad={handleComponentLoad} onError={handleError}>
            {children}
          </ComponentWrapper>
        </Suspense>
      )}
    </div>
  );
};

/**
 * Wrapper component to handle loading state
 */
const ComponentWrapper = ({ children, onLoad, onError }) => {
  useEffect(() => {
    // Component has mounted successfully
    onLoad();
  }, [onLoad]);

  // Error boundary for the wrapped component
  useEffect(() => {
    const handleUnhandledRejection = (event) => {
      onError(event.reason);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  try {
    return children;
  } catch (error) {
    onError(error);
    return null;
  }
};

export default PersistentSuspense;

/**
 * Hook for managing persistent component state
 */
export const usePersistentComponent = (componentId) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [shouldKeepMounted, setShouldKeepMounted] = useState(true);

  const markAsLoaded = () => {
    setIsLoaded(true);
  };

  const toggleKeepMounted = (keep = true) => {
    setShouldKeepMounted(keep);
  };

  return {
    isLoaded,
    shouldKeepMounted,
    markAsLoaded,
    toggleKeepMounted
  };
};

/**
 * Higher-order component for persistent mounting
 */
export const withPersistentMount = (WrappedComponent, options = {}) => {
  const { 
    keepMounted = true, 
    fallback = <div>Loading...</div>,
    displayName = 'PersistentComponent'
  } = options;

  const PersistentComponent = (props) => {
    return (
      <PersistentSuspense 
        fallback={fallback} 
        keepMounted={keepMounted}
      >
        <WrappedComponent {...props} />
      </PersistentSuspense>
    );
  };

  PersistentComponent.displayName = displayName;
  return PersistentComponent;
};
