import Pinterest from "../../assets/Hero/Pinterest.svg";
import Instagram from "../../assets/Hero/Instagram.svg";
import LinkedIn from "../../assets/Hero/LinkedIn.svg";
import Dummy_01 from "../../assets/Hero/Dummy-01.avif";
import Dummy_02 from "../../assets/Hero/Dummy-02.avif";
import Dummy_03 from "../../assets/Hero/Dummy-03.avif";
import Dummy_04 from "../../assets/Hero/Dummy-04.avif";
import Dummy_05 from "../../assets/Hero/Dummy-05.avif";
import { useState, useEffect, memo } from "react";

// Create a global cache for loaded images to prevent re-loading
const imageCache = new Set();
const loadingPromises = new Map();

// Image configurations for better maintainability
const imageConfigs = {
  mobile: {
    row1: [
      { src: Pinterest, alt: "Pinterest", height: "h-[120px]" },
      { src: Dummy_01, alt: "Dummy", height: "h-[120px]" },
      { src: Instagram, alt: "Instagram", height: "h-[120px]" },
      { src: Dummy_02, alt: "Dummy", height: "h-[120px]" },
    ],
    row2: [
      { src: Dummy_03, alt: "Dummy-03", height: "h-[120px]" },
      { src: LinkedIn, alt: "LinkedIn", height: "h-[120px]" },
      { src: Dummy_04, alt: "Dummy-04", height: "h-[120px]" },
      { src: Dummy_05, alt: "Dummy-05", height: "h-[120px]" },
    ],
  },
  desktop: {
    leftColumn: [
      { src: Pinterest, alt: "Pinterest", height: "h-[150px] lg:h-[169px]" },
      { src: Dummy_01, alt: "Dummy", height: "h-[270px] lg:h-[310px]" },
      { src: Instagram, alt: "Instagram", height: "h-[150px] lg:h-[173px]" },
      { src: Dummy_02, alt: "Dummy", height: "h-[220px] lg:h-[250px]" },
    ],
    rightColumn: [
      { src: Dummy_03, alt: "Dummy-03", height: "h-[220px] lg:h-[250px]" },
      { src: LinkedIn, alt: "LinkedIn", height: "h-[150px] lg:h-[169px]" },
      { src: Dummy_04, alt: "Dummy-04", height: "h-[220px] lg:h-[250px]" },
      { src: Dummy_05, alt: "Dummy-05", height: "h-[210px] lg:h-[240px]" },
    ],
  },
};

// Reusable components for image display
const MobileImage = ({ src, alt, priority = false }) => (
  <div className="flex-shrink-0 mr-4 transform-gpu">
    <img
      src={src}
      alt={alt}
      className="w-[120px] h-[120px] object-cover rounded-full will-change-transform"
      loading={priority ? "eager" : "lazy"}
      decoding="async"
      width={120}
      height={120}
      style={{ backfaceVisibility: "hidden" }}
    />
  </div>
);

const DesktopImage = ({ src, alt, height, priority = false }) => (
  <div className="mb-3 md:mb-4">
    <img
      src={src}
      alt={alt}
      className={`w-[220px] lg:w-[250px] ${height} object-cover rounded-2xl`}
      loading={priority ? "eager" : "lazy"}
      decoding="async"
      width={250}
    />
  </div>
);

const ScrollingRow = memo(({ images, direction, className = "" }) => {
  // Triple images for truly seamless infinite scroll
  const tripleImages = [...images, ...images, ...images];

  return (
    <div className={`w-full h-[120px] overflow-hidden ${className}`}>
      <div
        className={`animate-scroll-${direction} flex flex-row transform-gpu will-change-transform`}
        style={{ width: "max-content" }}
      >
        {tripleImages.map((img, idx) => (
          <MobileImage
            key={`${img.alt}-${idx}`}
            {...img}
            priority={idx < 4} // Prioritize first 4 images for faster loading
          />
        ))}
      </div>
    </div>
  );
});

const ScrollingColumn = memo(({ images, direction, className = "" }) => {
  // Double the images for smooth infinite scroll
  const doubleImages = [...images, ...images];

  return (
    <div className={`h-screen overflow-hidden ${className}`}>
      <div
        className={`animate-scroll-${direction} flex flex-col gap-2 transform-gpu will-change-transform`}
      >
        {doubleImages.map((img, idx) => (
          <DesktopImage
            key={`${img.alt}-${idx}`}
            {...img}
            priority={idx < 2} // Prioritize first 2 images for faster loading
          />
        ))}
      </div>
    </div>
  );
});

// Skeleton Loader Components
const SkeletonCircle = () => (
  <div className="w-[120px] h-[120px] bg-gray-200 animate-pulse rounded-full mr-4" />
);
const SkeletonRect = ({ height }) => (
  <div
    className={`w-[220px] lg:w-[250px] ${height} bg-gray-200 animate-pulse rounded-2xl mb-3 md:mb-4`}
  />
);
const MobileSkeleton = () => (
  <div className="w-full flex flex-col gap-4 pb-[20px] md:pb-[0px]">
    <div className="flex flex-row">
      {[...Array(4)].map((_, i) => (
        <SkeletonCircle key={i} />
      ))}
    </div>
    <div className="flex flex-row">
      {[...Array(4)].map((_, i) => (
        <SkeletonCircle key={i} />
      ))}
    </div>
  </div>
);
const DesktopSkeleton = () => (
  <div className="flex">
    <div className="flex flex-col mr-2 md:mr-4">
      {[
        imageConfigs.desktop.leftColumn[0],
        imageConfigs.desktop.leftColumn[1],
        imageConfigs.desktop.leftColumn[2],
        imageConfigs.desktop.leftColumn[3],
      ].map((img, i) => (
        <SkeletonRect key={i} height={img.height} />
      ))}
    </div>
    <div className="flex flex-col ml-2 md:ml-4">
      {[
        imageConfigs.desktop.rightColumn[0],
        imageConfigs.desktop.rightColumn[1],
        imageConfigs.desktop.rightColumn[2],
        imageConfigs.desktop.rightColumn[3],
      ].map((img, i) => (
        <SkeletonRect key={i} height={img.height} />
      ))}
    </div>
  </div>
);

function Main_section() {
  // Track image loading
  const [imagesLoaded, setImagesLoaded] = useState(false);

  // Gather all image srcs for both mobile and desktop
  const allImageSrcs = [
    ...imageConfigs.mobile.row1,
    ...imageConfigs.mobile.row2,
    ...imageConfigs.desktop.leftColumn,
    ...imageConfigs.desktop.rightColumn,
  ].map((img) => img.src);

  useEffect(() => {
    // Check if images are already cached
    const allCached = allImageSrcs.every((src) => imageCache.has(src));

    if (allCached) {
      // All images are already loaded, show immediately
      setImagesLoaded(true);
      return;
    }

    let loaded = 0;
    const total = allImageSrcs.length;

    // Add timeout to prevent infinite loading
    const fallbackTimeout = setTimeout(() => {
      setImagesLoaded(true);
    }, 1500); // Reduced timeout

    const checkComplete = () => {
      loaded++;
      if (loaded === total) {
        clearTimeout(fallbackTimeout);
        setImagesLoaded(true);
      }
    };

    allImageSrcs.forEach((src) => {
      // Check if already cached
      if (imageCache.has(src)) {
        checkComplete();
        return;
      }

      // Check if already loading
      if (loadingPromises.has(src)) {
        loadingPromises.get(src).then(checkComplete).catch(checkComplete);
        return;
      }

      // Create new loading promise
      const loadPromise = new Promise((resolve, reject) => {
        const img = new window.Image();
        img.src = src;
        img.onload = () => {
          imageCache.add(src);
          loadingPromises.delete(src);
          resolve();
        };
        img.onerror = () => {
          loadingPromises.delete(src);
          reject();
        };
      });

      loadingPromises.set(src, loadPromise);
      loadPromise.then(checkComplete).catch(checkComplete);
    });

    // Cleanup
    return () => {
      clearTimeout(fallbackTimeout);
    };
    // eslint-disable-next-line
  }, []);

  return (
    <div
      className="md:min-h-screen w-full flex flex-col md:flex-row justify-center items-center bg-white md:px-8 lg:px-12"
      style={{
    
        opacity: 1,
        visibility: "visible",
        backgroundColor: "white",
      }}
    >
      {/* Left Side - Content */}
      <div className="w-full md:w-1/2 flex flex-col justify-center items-center text-center md:text-left md:items-start px-4 md:px-8 lg:px-12 py-8 md:py-0 mt-[74px] md:mt-0">
        <div className="w-full md:max-w-[615px] md:min-h-[336px]">
          <h1 className="text-[#563D39] text-[20px] sm:text-[32px] md:text-[50px] lg:text-[70px] leading-[110%] font-medium mb-6">
            Create
            <br />
            Schedule
            <br />
            Go Viral
            <br />
            <span className="text-[#563D39] text-[20px] sm:text-[32px] md:text-[50px] lg:text-[70px] leading-[110%] font-medium mb-6">
              All in One Place
            </span>
          </h1>
          <p className="text-[#00000099] text-[12px] sm:text-[18px] md:text-[20px] leading-[140%] font-light">
            Trusted by creators, loved by influencers, designed for growth.{" "}
            <br />
            Create smarter. Reach further. Stay ahead.
          </p>
        </div>
      </div>
      {/* Right Side - Image Gallery or Skeleton */}
      <div className="w-full md:w-1/2 flex justify-center items-center md:px-6 lg:px-8">
        {/* Fixed-size container to prevent layout shift */}
        <div className="w-full md:w-[540px] min-h-[280px] md:min-h-[700px] flex justify-center items-center">
          {/* Mobile Layout - Two Stacked Rows */}
          <div className="md:hidden w-full flex flex-col gap-4 pb-[20px] md:pb-[0px]">
            {!imagesLoaded ? (
              <MobileSkeleton />
            ) : (
              <>
                <ScrollingRow
                  images={imageConfigs.mobile.row1}
                  direction="left"
                />
                <ScrollingRow
                  images={imageConfigs.mobile.row2}
                  direction="right"
                />
              </>
            )}
          </div>
          {/* Desktop Layout - Vertical Columns */}
          <div className="hidden md:flex">
            {!imagesLoaded ? (
              <DesktopSkeleton />
            ) : (
              <>
                <ScrollingColumn
                  images={imageConfigs.desktop.leftColumn}
                  direction="up"
                  className="mr-2 md:mr-4"
                />
                <ScrollingColumn
                  images={imageConfigs.desktop.rightColumn}
                  direction="down"
                  className="ml-2 md:ml-4"
                />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Add display names for better debugging
ScrollingRow.displayName = "ScrollingRow";
ScrollingColumn.displayName = "ScrollingColumn";

export default memo(Main_section);
